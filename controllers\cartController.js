const { executeQuery, executeTransaction } = require('../config/database');
const { formatResponse } = require('../utils/helpers');

// @desc    Get user's cart
// @route   GET /api/cart
// @access  Private/Public (with session)
const getCart = async (req, res, next) => {
  try {
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return res.status(400).json(
        formatResponse(false, 'User ID or session ID required')
      );
    }

    let cartQuery, cartParams;

    if (userId) {
      cartQuery = `
        SELECT 
          c.*,
          p.name as product_name,
          p.slug as product_slug,
          p.price as current_price,
          p.sale_price as current_sale_price,
          p.stock_quantity,
          p.is_active,
          (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image,
          cat.name as category_name
        FROM cart c
        LEFT JOIN products p ON c.product_id = p.id
        LEFT JOIN categories cat ON p.category_id = cat.id
        WHERE c.user_id = ?
        ORDER BY c.created_at DESC
      `;
      cartParams = [userId];
    } else {
      cartQuery = `
        SELECT 
          c.*,
          p.name as product_name,
          p.slug as product_slug,
          p.price as current_price,
          p.sale_price as current_sale_price,
          p.stock_quantity,
          p.is_active,
          (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image,
          cat.name as category_name
        FROM cart c
        LEFT JOIN products p ON c.product_id = p.id
        LEFT JOIN categories cat ON p.category_id = cat.id
        WHERE c.session_id = ?
        ORDER BY c.created_at DESC
      `;
      cartParams = [sessionId];
    }

    const cartItems = await executeQuery(cartQuery, cartParams);

    // Calculate totals
    let subtotal = 0;
    const formattedItems = cartItems.map(item => {
      const finalPrice = item.current_sale_price || item.current_price;
      const itemTotal = finalPrice * item.quantity;
      subtotal += itemTotal;

      return {
        id: item.id,
        product_id: item.product_id,
        product_name: item.product_name,
        product_slug: item.product_slug,
        product_image: item.product_image,
        category_name: item.category_name,
        quantity: item.quantity,
        selected_color: item.selected_color,
        selected_size: item.selected_size,
        unit_price: finalPrice,
        original_price: item.current_price,
        sale_price: item.current_sale_price,
        total_price: itemTotal,
        stock_quantity: item.stock_quantity,
        is_available: item.is_active && item.stock_quantity >= item.quantity,
        created_at: item.created_at
      };
    });

    const tax = subtotal * 0.08; // 8% tax
    const shipping = subtotal >= 75 ? 0 : 5.99; // Free shipping over $75
    const total = subtotal + tax + shipping;

    res.status(200).json(
      formatResponse(true, 'Cart retrieved successfully', {
        items: formattedItems,
        summary: {
          item_count: formattedItems.length,
          total_quantity: formattedItems.reduce((sum, item) => sum + item.quantity, 0),
          subtotal: parseFloat(subtotal.toFixed(2)),
          tax: parseFloat(tax.toFixed(2)),
          shipping: parseFloat(shipping.toFixed(2)),
          total: parseFloat(total.toFixed(2))
        }
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Add item to cart
// @route   POST /api/cart
// @access  Private/Public (with session)
const addToCart = async (req, res, next) => {
  try {
    const { product_id, quantity = 1, selected_color, selected_size } = req.body;
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return res.status(400).json(
        formatResponse(false, 'User ID or session ID required')
      );
    }

    // Check if product exists and is active
    const products = await executeQuery(
      'SELECT id, name, price, sale_price, stock_quantity, is_active FROM products WHERE id = ?',
      [product_id]
    );

    if (products.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Product not found')
      );
    }

    const product = products[0];

    if (!product.is_active) {
      return res.status(400).json(
        formatResponse(false, 'Product is not available')
      );
    }

    if (product.stock_quantity < quantity) {
      return res.status(400).json(
        formatResponse(false, 'Insufficient stock available')
      );
    }

    // Check if item already exists in cart
    let existingItemQuery, existingItemParams;

    if (userId) {
      existingItemQuery = 'SELECT * FROM cart WHERE user_id = ? AND product_id = ? AND selected_color = ? AND selected_size = ?';
      existingItemParams = [userId, product_id, selected_color || null, selected_size || null];
    } else {
      existingItemQuery = 'SELECT * FROM cart WHERE session_id = ? AND product_id = ? AND selected_color = ? AND selected_size = ?';
      existingItemParams = [sessionId, product_id, selected_color || null, selected_size || null];
    }

    const existingItems = await executeQuery(existingItemQuery, existingItemParams);

    const currentPrice = product.sale_price || product.price;

    if (existingItems.length > 0) {
      // Update existing item
      const existingItem = existingItems[0];
      const newQuantity = existingItem.quantity + quantity;

      if (product.stock_quantity < newQuantity) {
        return res.status(400).json(
          formatResponse(false, 'Cannot add more items. Insufficient stock available')
        );
      }

      await executeQuery(
        'UPDATE cart SET quantity = ?, price = ? WHERE id = ?',
        [newQuantity, currentPrice, existingItem.id]
      );

      res.status(200).json(
        formatResponse(true, 'Cart item updated successfully')
      );
    } else {
      // Add new item
      const insertQuery = userId 
        ? 'INSERT INTO cart (user_id, product_id, quantity, selected_color, selected_size, price) VALUES (?, ?, ?, ?, ?, ?)'
        : 'INSERT INTO cart (session_id, product_id, quantity, selected_color, selected_size, price) VALUES (?, ?, ?, ?, ?, ?)';
      
      const insertParams = userId
        ? [userId, product_id, quantity, selected_color || null, selected_size || null, currentPrice]
        : [sessionId, product_id, quantity, selected_color || null, selected_size || null, currentPrice];

      await executeQuery(insertQuery, insertParams);

      res.status(201).json(
        formatResponse(true, 'Item added to cart successfully')
      );
    }
  } catch (error) {
    next(error);
  }
};

// @desc    Update cart item
// @route   PUT /api/cart/:id
// @access  Private/Public (with session)
const updateCartItem = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { quantity } = req.body;
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return res.status(400).json(
        formatResponse(false, 'User ID or session ID required')
      );
    }

    // Get cart item
    let cartItemQuery, cartItemParams;

    if (userId) {
      cartItemQuery = 'SELECT c.*, p.stock_quantity FROM cart c LEFT JOIN products p ON c.product_id = p.id WHERE c.id = ? AND c.user_id = ?';
      cartItemParams = [id, userId];
    } else {
      cartItemQuery = 'SELECT c.*, p.stock_quantity FROM cart c LEFT JOIN products p ON c.product_id = p.id WHERE c.id = ? AND c.session_id = ?';
      cartItemParams = [id, sessionId];
    }

    const cartItems = await executeQuery(cartItemQuery, cartItemParams);

    if (cartItems.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Cart item not found')
      );
    }

    const cartItem = cartItems[0];

    if (cartItem.stock_quantity < quantity) {
      return res.status(400).json(
        formatResponse(false, 'Insufficient stock available')
      );
    }

    // Update cart item
    await executeQuery(
      'UPDATE cart SET quantity = ? WHERE id = ?',
      [quantity, id]
    );

    res.status(200).json(
      formatResponse(true, 'Cart item updated successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Remove item from cart
// @route   DELETE /api/cart/:id
// @access  Private/Public (with session)
const removeFromCart = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return res.status(400).json(
        formatResponse(false, 'User ID or session ID required')
      );
    }

    // Remove cart item
    let deleteQuery, deleteParams;

    if (userId) {
      deleteQuery = 'DELETE FROM cart WHERE id = ? AND user_id = ?';
      deleteParams = [id, userId];
    } else {
      deleteQuery = 'DELETE FROM cart WHERE id = ? AND session_id = ?';
      deleteParams = [id, sessionId];
    }

    const result = await executeQuery(deleteQuery, deleteParams);

    if (result.affectedRows === 0) {
      return res.status(404).json(
        formatResponse(false, 'Cart item not found')
      );
    }

    res.status(200).json(
      formatResponse(true, 'Item removed from cart successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Clear cart
// @route   DELETE /api/cart
// @access  Private/Public (with session)
const clearCart = async (req, res, next) => {
  try {
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return res.status(400).json(
        formatResponse(false, 'User ID or session ID required')
      );
    }

    // Clear cart
    let deleteQuery, deleteParams;

    if (userId) {
      deleteQuery = 'DELETE FROM cart WHERE user_id = ?';
      deleteParams = [userId];
    } else {
      deleteQuery = 'DELETE FROM cart WHERE session_id = ?';
      deleteParams = [sessionId];
    }

    await executeQuery(deleteQuery, deleteParams);

    res.status(200).json(
      formatResponse(true, 'Cart cleared successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Merge guest cart with user cart (after login)
// @route   POST /api/cart/merge
// @access  Private
const mergeCart = async (req, res, next) => {
  try {
    const { session_id } = req.body;
    const userId = req.user.id;

    if (!session_id) {
      return res.status(400).json(
        formatResponse(false, 'Session ID required')
      );
    }

    // Get guest cart items
    const guestCartItems = await executeQuery(
      'SELECT * FROM cart WHERE session_id = ?',
      [session_id]
    );

    if (guestCartItems.length === 0) {
      return res.status(200).json(
        formatResponse(true, 'No items to merge')
      );
    }

    // Merge items
    for (const guestItem of guestCartItems) {
      // Check if user already has this item
      const existingItems = await executeQuery(
        'SELECT * FROM cart WHERE user_id = ? AND product_id = ? AND selected_color = ? AND selected_size = ?',
        [userId, guestItem.product_id, guestItem.selected_color, guestItem.selected_size]
      );

      if (existingItems.length > 0) {
        // Update existing item quantity
        await executeQuery(
          'UPDATE cart SET quantity = quantity + ? WHERE id = ?',
          [guestItem.quantity, existingItems[0].id]
        );
      } else {
        // Add new item to user cart
        await executeQuery(
          'INSERT INTO cart (user_id, product_id, quantity, selected_color, selected_size, price) VALUES (?, ?, ?, ?, ?, ?)',
          [userId, guestItem.product_id, guestItem.quantity, guestItem.selected_color, guestItem.selected_size, guestItem.price]
        );
      }
    }

    // Remove guest cart items
    await executeQuery(
      'DELETE FROM cart WHERE session_id = ?',
      [session_id]
    );

    res.status(200).json(
      formatResponse(true, 'Cart merged successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  mergeCart
};
