const { executeQuery, executeTransaction } = require('../config/database');
const { formatResponse, generateOrderNumber, calculateTax, calculateShipping } = require('../utils/helpers');

// @desc    Create new order
// @route   POST /api/orders
// @access  Private/Public (guest checkout)
const createOrder = async (req, res, next) => {
  try {
    const {
      shipping_address,
      billing_address,
      payment_method = 'credit_card',
      notes,
      guest_email
    } = req.body;

    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !guest_email) {
      return res.status(400).json(
        formatResponse(false, 'User authentication or guest email required')
      );
    }

    // Get cart items
    let cartQuery, cartParams;
    if (userId) {
      cartQuery = `
        SELECT c.*, p.name, p.sku, p.price as current_price, p.sale_price, p.stock_quantity, p.weight
        FROM cart c
        LEFT JOIN products p ON c.product_id = p.id
        WHERE c.user_id = ? AND p.is_active = 1
      `;
      cartParams = [userId];
    } else {
      cartQuery = `
        SELECT c.*, p.name, p.sku, p.price as current_price, p.sale_price, p.stock_quantity, p.weight
        FROM cart c
        LEFT JOIN products p ON c.product_id = p.id
        WHERE c.session_id = ? AND p.is_active = 1
      `;
      cartParams = [sessionId];
    }

    const cartItems = await executeQuery(cartQuery, cartParams);

    if (cartItems.length === 0) {
      return res.status(400).json(
        formatResponse(false, 'Cart is empty')
      );
    }

    // Validate stock availability
    for (const item of cartItems) {
      if (item.stock_quantity < item.quantity) {
        return res.status(400).json(
          formatResponse(false, `Insufficient stock for ${item.name}`)
        );
      }
    }

    // Calculate order totals
    let subtotal = 0;
    let totalWeight = 0;
    const orderItems = cartItems.map(item => {
      const unitPrice = item.sale_price || item.current_price;
      const totalPrice = unitPrice * item.quantity;
      subtotal += totalPrice;
      totalWeight += (item.weight || 0) * item.quantity;

      return {
        product_id: item.product_id,
        product_name: item.name,
        product_sku: item.sku,
        quantity: item.quantity,
        unit_price: unitPrice,
        total_price: totalPrice,
        selected_color: item.selected_color,
        selected_size: item.selected_size,
        product_snapshot: JSON.stringify({
          name: item.name,
          sku: item.sku,
          original_price: item.current_price,
          sale_price: item.sale_price,
          selected_color: item.selected_color,
          selected_size: item.selected_size
        })
      };
    });

    const taxAmount = calculateTax(subtotal);
    const shippingAmount = calculateShipping(subtotal, totalWeight);
    const totalAmount = subtotal + taxAmount + shippingAmount;

    // Generate order number
    const orderNumber = generateOrderNumber();

    // Create order and order items in transaction
    const queries = [
      {
        query: `
          INSERT INTO orders (
            order_number, user_id, guest_email, status, payment_status, payment_method,
            subtotal, tax_amount, shipping_amount, total_amount,
            shipping_address, billing_address, notes
          ) VALUES (?, ?, ?, 'pending', 'pending', ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        params: [
          orderNumber,
          userId || null,
          guest_email || null,
          payment_method,
          subtotal,
          taxAmount,
          shippingAmount,
          totalAmount,
          JSON.stringify(shipping_address),
          JSON.stringify(billing_address),
          notes || null
        ]
      }
    ];

    // Add order items queries
    orderItems.forEach(item => {
      queries.push({
        query: `
          INSERT INTO order_items (
            order_id, product_id, product_name, product_sku, quantity,
            unit_price, total_price, selected_color, selected_size, product_snapshot
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
        params: [
          null, // Will be replaced with actual order_id
          item.product_id,
          item.product_name,
          item.product_sku,
          item.quantity,
          item.unit_price,
          item.total_price,
          item.selected_color,
          item.selected_size,
          item.product_snapshot
        ]
      });
    });

    // Update stock quantities
    cartItems.forEach(item => {
      queries.push({
        query: 'UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?',
        params: [item.quantity, item.product_id]
      });
    });

    // Clear cart
    if (userId) {
      queries.push({
        query: 'DELETE FROM cart WHERE user_id = ?',
        params: [userId]
      });
    } else {
      queries.push({
        query: 'DELETE FROM cart WHERE session_id = ?',
        params: [sessionId]
      });
    }

    // Execute transaction
    const results = await executeTransaction(queries.map((q, index) => {
      if (index > 0 && index <= orderItems.length) {
        // Replace order_id placeholder with actual order_id from first query
        q.params[0] = results?.[0]?.insertId || '{{ORDER_ID}}';
      }
      return q;
    }));

    const orderId = results[0].insertId;

    // Update order items with correct order_id
    for (let i = 0; i < orderItems.length; i++) {
      await executeQuery(
        `UPDATE order_items SET order_id = ? WHERE product_id = ? AND product_name = ? AND order_id IS NULL`,
        [orderId, orderItems[i].product_id, orderItems[i].product_name]
      );
    }

    // Get created order
    const order = await executeQuery(
      'SELECT * FROM orders WHERE id = ?',
      [orderId]
    );

    res.status(201).json(
      formatResponse(true, 'Order created successfully', {
        order: {
          ...order[0],
          shipping_address: JSON.parse(order[0].shipping_address),
          billing_address: JSON.parse(order[0].billing_address)
        },
        order_items: orderItems
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get user's orders
// @route   GET /api/orders
// @access  Private
const getOrders = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, status } = req.query;

    let whereClause = 'WHERE user_id = ?';
    let queryParams = [userId];

    if (status) {
      whereClause += ' AND status = ?';
      queryParams.push(status);
    }

    // Get total count
    const countResult = await executeQuery(
      `SELECT COUNT(*) as total FROM orders ${whereClause}`,
      queryParams
    );
    const totalCount = countResult[0].total;

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get orders
    const orders = await executeQuery(
      `SELECT 
        id, order_number, status, payment_status, payment_method,
        subtotal, tax_amount, shipping_amount, total_amount,
        created_at, shipped_at, delivered_at
       FROM orders 
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, parseInt(limit), offset]
    );

    res.status(200).json(
      formatResponse(true, 'Orders retrieved successfully', orders, {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page * limit < totalCount,
        hasPrevPage: page > 1
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
const getOrder = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Get order
    const orders = await executeQuery(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (orders.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Order not found')
      );
    }

    const order = orders[0];

    // Get order items
    const orderItems = await executeQuery(
      'SELECT * FROM order_items WHERE order_id = ?',
      [id]
    );

    res.status(200).json(
      formatResponse(true, 'Order retrieved successfully', {
        ...order,
        shipping_address: JSON.parse(order.shipping_address),
        billing_address: JSON.parse(order.billing_address),
        items: orderItems.map(item => ({
          ...item,
          product_snapshot: JSON.parse(item.product_snapshot)
        }))
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get order by order number (for guest users)
// @route   GET /api/orders/track/:orderNumber
// @access  Public
const trackOrder = async (req, res, next) => {
  try {
    const { orderNumber } = req.params;
    const { email } = req.query;

    if (!email) {
      return res.status(400).json(
        formatResponse(false, 'Email is required for order tracking')
      );
    }

    // Get order
    const orders = await executeQuery(
      'SELECT * FROM orders WHERE order_number = ? AND (guest_email = ? OR user_id IN (SELECT id FROM users WHERE email = ?))',
      [orderNumber, email, email]
    );

    if (orders.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Order not found')
      );
    }

    const order = orders[0];

    // Get order items
    const orderItems = await executeQuery(
      'SELECT product_name, quantity, unit_price, total_price, selected_color, selected_size FROM order_items WHERE order_id = ?',
      [order.id]
    );

    res.status(200).json(
      formatResponse(true, 'Order retrieved successfully', {
        order_number: order.order_number,
        status: order.status,
        payment_status: order.payment_status,
        total_amount: order.total_amount,
        created_at: order.created_at,
        shipped_at: order.shipped_at,
        delivered_at: order.delivered_at,
        shipping_address: JSON.parse(order.shipping_address),
        items: orderItems
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
const cancelOrder = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Get order
    const orders = await executeQuery(
      'SELECT * FROM orders WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (orders.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Order not found')
      );
    }

    const order = orders[0];

    if (order.status !== 'pending') {
      return res.status(400).json(
        formatResponse(false, 'Order cannot be cancelled')
      );
    }

    // Get order items to restore stock
    const orderItems = await executeQuery(
      'SELECT product_id, quantity FROM order_items WHERE order_id = ?',
      [id]
    );

    // Update order status and restore stock in transaction
    const queries = [
      {
        query: 'UPDATE orders SET status = "cancelled" WHERE id = ?',
        params: [id]
      }
    ];

    // Restore stock for each item
    orderItems.forEach(item => {
      queries.push({
        query: 'UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?',
        params: [item.quantity, item.product_id]
      });
    });

    await executeTransaction(queries);

    res.status(200).json(
      formatResponse(true, 'Order cancelled successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createOrder,
  getOrders,
  getOrder,
  trackOrder,
  cancelOrder
};
