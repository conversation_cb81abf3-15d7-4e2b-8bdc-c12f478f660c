const { executeQuery } = require('../config/database');
const { formatResponse } = require('../utils/helpers');

// @desc    Get user addresses
// @route   GET /api/users/addresses
// @access  Private
const getAddresses = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const addresses = await executeQuery(
      'SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC',
      [userId]
    );

    res.status(200).json(
      formatResponse(true, 'Addresses retrieved successfully', addresses)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Add new address
// @route   POST /api/users/addresses
// @access  Private
const addAddress = async (req, res, next) => {
  try {
    const {
      type,
      first_name,
      last_name,
      company,
      address_line_1,
      address_line_2,
      city,
      state,
      postal_code,
      country = 'United States',
      phone,
      is_default = false
    } = req.body;

    const userId = req.user.id;

    // If this is set as default, remove default from other addresses of same type
    if (is_default) {
      await executeQuery(
        'UPDATE user_addresses SET is_default = FALSE WHERE user_id = ? AND type = ?',
        [userId, type]
      );
    }

    // Add new address
    const result = await executeQuery(
      `INSERT INTO user_addresses (
        user_id, type, first_name, last_name, company, address_line_1, address_line_2,
        city, state, postal_code, country, phone, is_default
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId, type, first_name, last_name, company || null, address_line_1,
        address_line_2 || null, city, state, postal_code, country, phone || null, is_default
      ]
    );

    // Get created address
    const address = await executeQuery(
      'SELECT * FROM user_addresses WHERE id = ?',
      [result.insertId]
    );

    res.status(201).json(
      formatResponse(true, 'Address added successfully', address[0])
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update address
// @route   PUT /api/users/addresses/:id
// @access  Private
const updateAddress = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      type,
      first_name,
      last_name,
      company,
      address_line_1,
      address_line_2,
      city,
      state,
      postal_code,
      country,
      phone,
      is_default
    } = req.body;

    const userId = req.user.id;

    // Check if address exists and belongs to user
    const existingAddresses = await executeQuery(
      'SELECT * FROM user_addresses WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (existingAddresses.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Address not found')
      );
    }

    // If this is set as default, remove default from other addresses of same type
    if (is_default) {
      await executeQuery(
        'UPDATE user_addresses SET is_default = FALSE WHERE user_id = ? AND type = ? AND id != ?',
        [userId, type, id]
      );
    }

    // Update address
    await executeQuery(
      `UPDATE user_addresses SET 
        type = ?, first_name = ?, last_name = ?, company = ?, address_line_1 = ?,
        address_line_2 = ?, city = ?, state = ?, postal_code = ?, country = ?,
        phone = ?, is_default = ?
       WHERE id = ? AND user_id = ?`,
      [
        type, first_name, last_name, company || null, address_line_1,
        address_line_2 || null, city, state, postal_code, country,
        phone || null, is_default, id, userId
      ]
    );

    // Get updated address
    const address = await executeQuery(
      'SELECT * FROM user_addresses WHERE id = ?',
      [id]
    );

    res.status(200).json(
      formatResponse(true, 'Address updated successfully', address[0])
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Delete address
// @route   DELETE /api/users/addresses/:id
// @access  Private
const deleteAddress = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Delete address
    const result = await executeQuery(
      'DELETE FROM user_addresses WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json(
        formatResponse(false, 'Address not found')
      );
    }

    res.status(200).json(
      formatResponse(true, 'Address deleted successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Set default address
// @route   PUT /api/users/addresses/:id/default
// @access  Private
const setDefaultAddress = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Get address to check if it exists and get its type
    const addresses = await executeQuery(
      'SELECT type FROM user_addresses WHERE id = ? AND user_id = ?',
      [id, userId]
    );

    if (addresses.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Address not found')
      );
    }

    const addressType = addresses[0].type;

    // Remove default from other addresses of same type
    await executeQuery(
      'UPDATE user_addresses SET is_default = FALSE WHERE user_id = ? AND type = ?',
      [userId, addressType]
    );

    // Set this address as default
    await executeQuery(
      'UPDATE user_addresses SET is_default = TRUE WHERE id = ?',
      [id]
    );

    res.status(200).json(
      formatResponse(true, 'Default address updated successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get user statistics
// @route   GET /api/users/stats
// @access  Private
const getUserStats = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Get order statistics
    const orderStats = await executeQuery(
      `SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as completed_orders,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
        COALESCE(SUM(CASE WHEN status = 'delivered' THEN total_amount ELSE 0 END), 0) as total_spent
       FROM orders 
       WHERE user_id = ?`,
      [userId]
    );

    // Get wishlist count
    const wishlistStats = await executeQuery(
      'SELECT COUNT(*) as wishlist_items FROM wishlist WHERE user_id = ?',
      [userId]
    );

    // Get cart count
    const cartStats = await executeQuery(
      'SELECT COUNT(*) as cart_items FROM cart WHERE user_id = ?',
      [userId]
    );

    // Get recent orders
    const recentOrders = await executeQuery(
      `SELECT id, order_number, status, total_amount, created_at 
       FROM orders 
       WHERE user_id = ? 
       ORDER BY created_at DESC 
       LIMIT 5`,
      [userId]
    );

    const stats = {
      orders: orderStats[0],
      wishlist_items: wishlistStats[0].wishlist_items,
      cart_items: cartStats[0].cart_items,
      recent_orders: recentOrders
    };

    res.status(200).json(
      formatResponse(true, 'User statistics retrieved successfully', stats)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get user reviews
// @route   GET /api/users/reviews
// @access  Private
const getUserReviews = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;

    // Get total count
    const countResult = await executeQuery(
      'SELECT COUNT(*) as total FROM reviews WHERE user_id = ?',
      [userId]
    );
    const totalCount = countResult[0].total;

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Get reviews
    const reviews = await executeQuery(
      `SELECT 
        r.*,
        p.name as product_name,
        p.slug as product_slug,
        (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as product_image
       FROM reviews r
       LEFT JOIN products p ON r.product_id = p.id
       WHERE r.user_id = ?
       ORDER BY r.created_at DESC
       LIMIT ? OFFSET ?`,
      [userId, parseInt(limit), offset]
    );

    res.status(200).json(
      formatResponse(true, 'User reviews retrieved successfully', reviews, {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalCount / limit),
        totalCount,
        hasNextPage: page * limit < totalCount,
        hasPrevPage: page > 1
      })
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAddresses,
  addAddress,
  updateAddress,
  deleteAddress,
  setDefaultAddress,
  getUserStats,
  getUserReviews
};
