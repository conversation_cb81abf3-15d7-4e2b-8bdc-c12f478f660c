<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Fixes Verification</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-center mb-8 text-gray-800">
                🎯 Button Functionality Verification
            </h1>
            
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <h2 class="font-bold">✅ Critical Fix Applied</h2>
                <p>CSS class conflicts in modal elements have been resolved. All buttons should now work correctly.</p>
            </div>
            
            <!-- Test Results Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div id="login-test-result" class="text-6xl mb-4">⏳</div>
                    <h3 class="font-semibold text-lg mb-2">Login Modal</h3>
                    <button onclick="testLogin()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Test Login
                    </button>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div id="signup-test-result" class="text-6xl mb-4">⏳</div>
                    <h3 class="font-semibold text-lg mb-2">Signup Modal</h3>
                    <button onclick="testSignup()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Test Signup
                    </button>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div id="cart-test-result" class="text-6xl mb-4">⏳</div>
                    <h3 class="font-semibold text-lg mb-2">Add to Cart</h3>
                    <button onclick="testCart()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        Test Cart
                    </button>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div id="api-test-result" class="text-6xl mb-4">⏳</div>
                    <h3 class="font-semibold text-lg mb-2">API Connection</h3>
                    <button onclick="testAPI()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Test API
                    </button>
                </div>
            </div>
            
            <!-- Run All Tests -->
            <div class="text-center mb-8">
                <button onclick="runAllTests()" class="bg-red-600 text-white px-8 py-4 rounded-lg text-xl font-semibold hover:bg-red-700 transform hover:scale-105 transition-all duration-300">
                    🚀 Run All Tests
                </button>
            </div>
            
            <!-- Test Log -->
            <div class="bg-gray-900 text-green-400 rounded-lg p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Test Log</h3>
                    <button onclick="clearLog()" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                        Clear
                    </button>
                </div>
                <div id="test-log" class="font-mono text-sm max-h-96 overflow-y-auto">
                    <div class="text-yellow-400">🔧 Verification page loaded. Ready to test button functionality...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include main application scripts -->
    <script src="/api-config.js"></script>
    <script src="/script.js"></script>
    
    <script>
        // Test logging
        const testLog = document.getElementById('test-log');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            switch(type) {
                case 'success':
                    div.className = 'text-green-400';
                    div.innerHTML = `[${timestamp}] ✅ ${message}`;
                    break;
                case 'error':
                    div.className = 'text-red-400';
                    div.innerHTML = `[${timestamp}] ❌ ${message}`;
                    break;
                case 'warning':
                    div.className = 'text-yellow-400';
                    div.innerHTML = `[${timestamp}] ⚠️ ${message}`;
                    break;
                default:
                    div.className = 'text-blue-400';
                    div.innerHTML = `[${timestamp}] ℹ️ ${message}`;
            }
            
            testLog.appendChild(div);
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        function setResult(testId, success) {
            const element = document.getElementById(testId);
            element.textContent = success ? '✅' : '❌';
            element.className = success ? 'text-6xl mb-4 text-green-500' : 'text-6xl mb-4 text-red-500';
        }
        
        // Test functions
        function testLogin() {
            log('Testing login modal functionality...');
            
            try {
                if (typeof openLoginModal !== 'function') {
                    throw new Error('openLoginModal function not found');
                }
                
                openLoginModal();
                
                // Check if modal is visible
                setTimeout(() => {
                    const modal = document.getElementById('login-modal');
                    const isVisible = modal && !modal.classList.contains('hidden');
                    
                    if (isVisible) {
                        log('Login modal opened successfully', 'success');
                        setResult('login-test-result', true);
                        
                        // Close modal
                        setTimeout(() => {
                            closeAuthModal();
                            log('Login modal closed successfully', 'success');
                        }, 1000);
                    } else {
                        log('Login modal failed to open', 'error');
                        setResult('login-test-result', false);
                    }
                }, 100);
                
            } catch (error) {
                log(`Login test failed: ${error.message}`, 'error');
                setResult('login-test-result', false);
            }
        }
        
        function testSignup() {
            log('Testing signup modal functionality...');
            
            try {
                if (typeof openSignupModal !== 'function') {
                    throw new Error('openSignupModal function not found');
                }
                
                openSignupModal();
                
                // Check if modal is visible
                setTimeout(() => {
                    const modal = document.getElementById('signup-modal');
                    const isVisible = modal && !modal.classList.contains('hidden');
                    
                    if (isVisible) {
                        log('Signup modal opened successfully', 'success');
                        setResult('signup-test-result', true);
                        
                        // Close modal
                        setTimeout(() => {
                            closeAuthModal();
                            log('Signup modal closed successfully', 'success');
                        }, 1000);
                    } else {
                        log('Signup modal failed to open', 'error');
                        setResult('signup-test-result', false);
                    }
                }, 100);
                
            } catch (error) {
                log(`Signup test failed: ${error.message}`, 'error');
                setResult('signup-test-result', false);
            }
        }
        
        function testCart() {
            log('Testing add to cart functionality...');
            
            try {
                if (typeof addToCart !== 'function') {
                    throw new Error('addToCart function not found');
                }
                
                addToCart('test-product', 'Verification Test Product', 29.99);
                log('Add to cart function called successfully', 'success');
                setResult('cart-test-result', true);
                
            } catch (error) {
                log(`Cart test failed: ${error.message}`, 'error');
                setResult('cart-test-result', false);
            }
        }
        
        function testAPI() {
            log('Testing API connectivity...');
            
            fetch('/api/products')
                .then(response => {
                    if (response.ok) {
                        log('API connection successful', 'success');
                        setResult('api-test-result', true);
                        return response.json();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(data => {
                    log(`API returned ${data.data ? data.data.length : 0} products`, 'info');
                })
                .catch(error => {
                    log(`API test failed: ${error.message}`, 'error');
                    setResult('api-test-result', false);
                });
        }
        
        function runAllTests() {
            log('🚀 Starting comprehensive button functionality test...', 'info');
            
            // Reset all results
            ['login-test-result', 'signup-test-result', 'cart-test-result', 'api-test-result'].forEach(id => {
                document.getElementById(id).textContent = '⏳';
                document.getElementById(id).className = 'text-6xl mb-4 text-gray-400';
            });
            
            // Run tests with delays
            setTimeout(() => testLogin(), 500);
            setTimeout(() => testSignup(), 3000);
            setTimeout(() => testCart(), 5500);
            setTimeout(() => testAPI(), 6000);
            
            setTimeout(() => {
                log('🏁 All tests completed!', 'success');
            }, 7000);
        }
        
        function clearLog() {
            testLog.innerHTML = '<div class="text-yellow-400">🧹 Log cleared</div>';
        }
        
        // Auto-run basic checks on load
        document.addEventListener('DOMContentLoaded', () => {
            log('🔧 Verification page initialized');
            
            // Check if main functions exist
            const functions = ['openLoginModal', 'openSignupModal', 'closeAuthModal', 'addToCart'];
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    log(`✅ ${func} function available`);
                } else {
                    log(`❌ ${func} function missing`, 'error');
                }
            });
            
            // Check if modal elements exist
            const elements = ['login-modal', 'signup-modal'];
            elements.forEach(id => {
                if (document.getElementById(id)) {
                    log(`✅ ${id} element found`);
                } else {
                    log(`❌ ${id} element missing`, 'error');
                }
            });
            
            log('Ready to run tests. Click "Run All Tests" or test individual components.');
        });
    </script>
</body>
</html>
