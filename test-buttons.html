<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Functionality Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Button Functionality Test Page</h1>
        
        <!-- Test Results -->
        <div id="test-results" class="mb-8 space-y-4"></div>
        
        <!-- Manual Button Tests -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Manual Button Tests</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Authentication Tests -->
                <div class="border rounded-lg p-4">
                    <h3 class="font-medium mb-3">Authentication Buttons</h3>
                    <div class="space-y-2">
                        <button onclick="openLoginModal()" class="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">
                            Test Login Modal
                        </button>
                        <button onclick="openSignupModal()" class="w-full bg-green-500 text-white p-2 rounded hover:bg-green-600">
                            Test Signup Modal
                        </button>
                        <button onclick="closeAuthModal()" class="w-full bg-gray-500 text-white p-2 rounded hover:bg-gray-600">
                            Close Modals
                        </button>
                    </div>
                </div>
                
                <!-- Cart Tests -->
                <div class="border rounded-lg p-4">
                    <h3 class="font-medium mb-3">Cart Buttons</h3>
                    <div class="space-y-2">
                        <button onclick="addToCart('dress-1', 'Test Dress', 89.99)" class="w-full bg-purple-500 text-white p-2 rounded hover:bg-purple-600">
                            Test Add to Cart (Dress)
                        </button>
                        <button onclick="addToCart(1, 'API Product', 99.99)" class="w-full bg-indigo-500 text-white p-2 rounded hover:bg-indigo-600">
                            Test Add to Cart (API)
                        </button>
                        <button onclick="testCartFunctions()" class="w-full bg-yellow-500 text-white p-2 rounded hover:bg-yellow-600">
                            Test All Cart Functions
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Automated Test -->
            <div class="mt-6 text-center">
                <button onclick="runAutomatedTests()" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 font-semibold">
                    Run Automated Tests
                </button>
            </div>
        </div>
        
        <!-- Console Output -->
        <div class="bg-gray-900 text-green-400 rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-2">Console Output</h3>
            <div id="console-output" class="font-mono text-sm max-h-64 overflow-y-auto">
                <div>Console output will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Include API Config and Scripts -->
    <script src="/api-config.js"></script>
    <script src="/script.js"></script>
    
    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.className = type === 'error' ? 'text-red-400' : 'text-green-400';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Test functions
        function testCartFunctions() {
            console.log('Testing cart functions...');
            
            // Test cart UI update
            if (typeof updateCartUI === 'function') {
                updateCartUI();
                console.log('✅ updateCartUI function works');
            } else {
                console.error('❌ updateCartUI function not found');
            }
            
            // Test cart loading
            if (typeof loadCartFromAPI === 'function') {
                loadCartFromAPI();
                console.log('✅ loadCartFromAPI function works');
            } else {
                console.error('❌ loadCartFromAPI function not found');
            }
        }
        
        function runAutomatedTests() {
            console.log('=== STARTING AUTOMATED BUTTON TESTS ===');
            
            // Test if functions exist
            const functions = [
                'openLoginModal',
                'openSignupModal', 
                'closeAuthModal',
                'addToCart',
                'logout'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    console.log(`✅ ${funcName} function exists`);
                } else {
                    console.error(`❌ ${funcName} function missing`);
                }
            });
            
            // Test DOM elements
            const elements = [
                'login-modal',
                'signup-modal',
                'cart-btn',
                'cart-dropdown'
            ];
            
            elements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    console.log(`✅ Element ${elementId} found`);
                } else {
                    console.error(`❌ Element ${elementId} missing`);
                }
            });
            
            // Run the built-in test function if available
            if (typeof window.testButtonFunctionality === 'function') {
                console.log('Running built-in test function...');
                window.testButtonFunctionality();
            } else {
                console.error('❌ Built-in test function not available');
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Test page loaded successfully');
            console.log('Available test functions: runAutomatedTests(), testCartFunctions()');
        });
    </script>
</body>
</html>
